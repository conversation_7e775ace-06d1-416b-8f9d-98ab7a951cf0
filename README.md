# 🌟 الموقع الشخصي لحذيفة عبدالمعز

الموقع الشخصي لحذيفة عبدالمعز الحذيفي - مهندس تقنية معلومات ومطور ويب متخصص في Laravel وتطوير التطبيقات.

## 🚀 التقنيات المستخدمة

- **React 18** مع TypeScript للتطوير الآمن والقوي
- **Vite** لسرعة التطوير والبناء
- **Tailwind CSS** للتصميم السريع والمرن
- **ShadCN/UI** مكتبة مكونات UI متقدمة
- **React Router** للتنقل بين الصفحات
- **React Query** لإدارة البيانات والـ API
- **Lucide React** للأيقونات الجميلة

## 📋 الميزات

- ✅ تصميم متجاوب (Responsive Design)
- ✅ دعم الوضع المظلم/الفاتح
- ✅ دعم كامل للغة العربية (RTL)
- ✅ رسوم متحركة متقنة
- ✅ تحسين محركات البحث (SEO)
- ✅ أخبار تقنية حية من NewsData API
- ✅ معرض أعمال تفاعلي
- ✅ نموذج تواصل متقدم

## 🛠️ التثبيت والتشغيل

### المتطلبات
- Node.js (الإصدار 18 أو أحدث)
- npm أو yarn

### خطوات التثبيت

```bash
# 1. استنساخ المستودع
git clone https://github.com/HA1234098765/hodifa-portfolio.git

# 2. الانتقال إلى مجلد المشروع
cd hodifa-portfolio

# 3. تثبيت التبعيات
npm install

# 4. تشغيل الخادم المحلي
npm run dev
```

### البناء للإنتاج

```bash
# بناء المشروع للإنتاج
npm run build

# معاينة البناء محلياً
npm run preview
```

## 📁 هيكل المشروع

```
src/
├── components/          # المكونات المشتركة
│   ├── ui/             # مكونات ShadCN UI
│   ├── Navigation.tsx  # شريط التنقل
│   └── Footer.tsx      # تذييل الصفحة
├── pages/              # صفحات التطبيق
│   ├── Index.tsx       # الصفحة الرئيسية
│   ├── About.tsx       # صفحة نبذة عني
│   ├── Projects.tsx    # صفحة المشاريع
│   ├── Contact.tsx     # صفحة التواصل
│   └── TechNews.tsx    # صفحة الأخبار التقنية
├── hooks/              # React Hooks مخصصة
├── lib/                # المكتبات والأدوات المساعدة
└── App.tsx             # المكون الرئيسي
```

## 🌐 النشر

المشروع مُعد للنشر على GitHub Pages. لنشر المشروع:

1. ادفع التغييرات إلى GitHub
2. فعّل GitHub Pages في إعدادات المستودع
3. اختر مجلد `gh-pages` كمصدر

## 📧 التواصل

- **البريد الإلكتروني:** <EMAIL>
- **الهاتف:** +967 777548421 | +967 718706242
- **LinkedIn:** [hodifa-al-hodify](https://www.linkedin.com/in/hodifa-al-hodify-30644b289)
- **GitHub:** [HA1234098765](https://github.com/HA1234098765)

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

---

**تم التطوير بـ ❤️ بواسطة حذيفة عبدالمعز الحذيفي**
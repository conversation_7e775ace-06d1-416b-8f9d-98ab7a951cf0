
import { useState, useEffect } from "react";
import { ExternalLink, Clock, Globe } from "lucide-react";
import Navigation from "@/components/Navigation";
import Footer from "@/components/Footer";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";

const TechNews = () => {
  const [news, setNews] = useState([]);
  const [loading, setLoading] = useState(true);

  // Mock news data (في التطبيق الحقيقي ستأتي من API)
  const mockNews = [
    {
      id: 1,
      title: "تطورات جديدة في الذكاء الاصطناعي لعام 2024",
      summary: "اكتشف أحدث التطورات في مجال الذكاء الاصطناعي وكيف تؤثر على مستقبل التكنولوجيا...",
      url: "https://techcrunch.com/ai-developments-2024",
      source: "TechCrunch",
      publishedAt: "2024-01-15T10:30:00Z",
      category: "ذكاء اصطناعي"
    },
    {
      id: 2,
      title: "Laravel 11: المميزات الجديدة ونصائح التطوير",
      summary: "دليل شامل لأحدث إصدار من Laravel وكيفية الاستفادة من المميزات الجديدة في مشاريعك...",
      url: "https://laravel-news.com/laravel-11-features",
      source: "Laravel News",
      publishedAt: "2024-01-14T14:20:00Z",
      category: "تطوير ويب"
    },
    {
      id: 3,
      title: "أفضل ممارسات تصميم قواعد البيانات في 2024",
      summary: "تعلم أحدث الممارسات والتقنيات لتصميم قواعد بيانات فعالة وقابلة للتطوير...",
      url: "https://db-trends.com/best-practices-2024",
      source: "Database Trends",
      publishedAt: "2024-01-13T09:15:00Z",
      category: "قواعد البيانات"
    },
    {
      id: 4,
      title: "React vs Vue vs Angular: مقارنة شاملة لعام 2024",
      summary: "تحليل مفصل لأشهر أطر عمل JavaScript ومساعدتك في اختيار الأنسب لمشروعك القادم...",
      url: "https://frontend-weekly.com/frameworks-comparison-2024",
      source: "Frontend Weekly",
      publishedAt: "2024-01-12T16:45:00Z",
      category: "تطوير واجهات"
    },
    {
      id: 5,
      title: "الأمن السيبراني للمطورين: دليل المبتدئين",
      summary: "نصائح وممارسات أساسية لضمان أمان تطبيقاتك الويب من التهديدات الشائعة...",
      url: "https://cybersec-dev.com/security-guide-beginners",
      source: "CyberSec Dev",
      publishedAt: "2024-01-11T11:30:00Z",
      category: "أمن المعلومات"
    },
    {
      id: 6,
      title: "مستقبل تطوير الويب: التقنيات الناشئة",
      summary: "استكشف التقنيات الجديدة التي ستشكل مستقبل تطوير الويب في السنوات القادمة...",
      url: "https://web-future.com/emerging-technologies",
      source: "Web Future",
      publishedAt: "2024-01-10T13:20:00Z",
      category: "اتجاهات تقنية"
    }
  ];

  useEffect(() => {
    // محاكاة تحميل البيانات
    setTimeout(() => {
      setNews(mockNews);
      setLoading(false);
    }, 1000);
  }, []);

  const formatDate = (dateString) => {
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('ar-SA', {
      year: 'numeric',
      month: 'long',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date);
  };

  const getCategoryColor = (category) => {
    const colors = {
      "ذكاء اصطناعي": "bg-purple-500/20 text-purple-400",
      "تطوير ويب": "bg-blue-500/20 text-blue-400",
      "قواعد البيانات": "bg-green-500/20 text-green-400",
      "تطوير واجهات": "bg-cyan-500/20 text-cyan-400",
      "أمن المعلومات": "bg-red-500/20 text-red-400",
      "اتجاهات تقنية": "bg-amber-500/20 text-amber-400"
    };
    return colors[category] || "bg-gray-500/20 text-gray-400";
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-900 to-black text-white">
      <Navigation />
      
      <div className="pt-20 pb-12">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
          {/* Header */}
          <div className="text-center mb-16">
            <h1 className="text-4xl lg:text-6xl font-bold mb-4">
              <span className="text-amber-400">الأخبار</span> التقنية
            </h1>
            <div className="w-24 h-1 bg-amber-400 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              آخر الأخبار والمقالات في عالم التكنولوجيا وتطوير البرمجيات
            </p>
          </div>

          {loading ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {[...Array(6)].map((_, index) => (
                <Card key={index} className="bg-gray-900/50 border-amber-500/20 animate-pulse">
                  <CardHeader>
                    <div className="h-4 bg-gray-700 rounded mb-2"></div>
                    <div className="h-3 bg-gray-700 rounded w-3/4"></div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      <div className="h-3 bg-gray-700 rounded"></div>
                      <div className="h-3 bg-gray-700 rounded"></div>
                      <div className="h-3 bg-gray-700 rounded w-2/3"></div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {news.map((article) => (
                <Card key={article.id} className="bg-gray-900/50 border-amber-500/20 hover:border-amber-500/40 transition-all duration-300 group">
                  <CardHeader>
                    <div className="flex justify-between items-start mb-3">
                      <span className={`px-2 py-1 rounded-full text-xs ${getCategoryColor(article.category)}`}>
                        {article.category}
                      </span>
                      <div className="flex items-center text-xs text-gray-400">
                        <Globe className="w-3 h-3 ml-1" />
                        {article.source}
                      </div>
                    </div>
                    <CardTitle className="text-lg text-amber-400 group-hover:text-amber-300 transition-colors line-clamp-2">
                      {article.title}
                    </CardTitle>
                  </CardHeader>
                  
                  <CardContent>
                    <p className="text-gray-300 text-sm mb-4 line-clamp-3 leading-relaxed">
                      {article.summary}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center text-xs text-gray-400">
                        <Clock className="w-3 h-3 ml-1" />
                        {formatDate(article.publishedAt)}
                      </div>
                      
                      <Button 
                        size="sm"
                        variant="ghost"
                        className="text-amber-400 hover:text-amber-300 hover:bg-amber-500/10 transition-all duration-300"
                        asChild
                      >
                        <a href={article.url} target="_blank" rel="noopener noreferrer">
                          <ExternalLink className="w-4 h-4 ml-1" />
                          اقرأ المزيد
                        </a>
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}

          {/* Newsletter Subscription */}
          <div className="mt-16">
            <Card className="bg-gray-900/30 border-amber-500/20 max-w-2xl mx-auto">
              <CardContent className="p-8 text-center">
                <h3 className="text-2xl font-bold text-amber-400 mb-4">ابق على اطلاع دائم</h3>
                <p className="text-gray-300 mb-6">
                  تابع آخر الأخبار والمقالات في عالم التكنولوجيا وتطوير البرمجيات
                </p>
                <div className="text-sm text-gray-400">
                  * الأخبار يتم تحديثها بانتظام من مصادر موثوقة
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>

      <Footer />
    </div>
  );
};

export default TechNews;

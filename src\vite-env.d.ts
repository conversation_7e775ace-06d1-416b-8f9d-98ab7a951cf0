/// <reference types="vite/client" />

interface ImportMetaEnv {
  readonly VITE_NEWSDATA_API_KEY: string
  readonly VITE_NEWSDATA_API_URL: string
  readonly VITE_APP_NAME: string
  readonly VITE_APP_URL: string
  readonly VITE_APP_DESCRIPTION: string
  readonly VITE_CONTACT_EMAIL: string
  readonly VITE_CONTACT_PHONE_1: string
  readonly VITE_CONTACT_PHONE_2: string
  readonly VITE_LINKEDIN_URL: string
  readonly VITE_GITHUB_URL: string
  readonly VITE_FACEBOOK_URL: string
  readonly VITE_TWITTER_URL: string
  readonly VITE_INSTAGRAM_URL: string
}

interface ImportMeta {
  readonly env: ImportMetaEnv
}
